
测试条件：
写入数据：HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000001+E
其中8985231227852409000001 持续++ 以此来区分数据；
4轮测试，每次写入5000条数据 读取5000条，首次写入时：
//      // 测试前清空Flash
      printf("Erasing entire chip...\r\n");
      if (SPI_FLASH_EraseChip() == HAL_OK) {
        printf("Chip erase: OK\r\n");
      } else {
        printf("Chip erase: FAIL\r\n");
      }

      // 手动重置环形缓冲区地址到起始位置
      printf("Resetting ring buffer to start position...\r\n");
      if (SPI_FLASH_ClearRingBuffer() == HAL_OK) {
        printf("Ring buffer reset: OK\r\n");
      } else {
        printf("Ring buffer reset: FAIL\r\n");
      }
      printf("After reset - Unread records: %lu\r\n", SPI_FLASH_GetRecordCount());

首次写入时执行了数据清空和地址归零，后面3次写入没有执行这个地址清零，所以，后面的写入应该是持续写入的，共写入20000条数据，正确读取16162条，之后开始错误；


Flash data: 0 unread
=== 1 SPI Flash Manual Read/Write 5000 ===
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
Internal Flash OK
SPI Flash OK
Flash data: 0 unread
=== 1 SPI Flash Manual Read/Write 5000 ===
Erasing entire chip...
Chip erase: OK
Resetting ring buffer to start position...
Ring buffer reset: OK
After reset - Unread records: 0
Write progress: 100/5000
..........
Write progress: 5000/5000
Write test completed. Unread records: 5000
=== Ring Buffer Status ===
Current Address: 0x0009EB10
Total Records: 5000
Read Records: 0
Unread Records: 5000
Max Capacity: 16131 records
Buffer Size: 2097152 bytes
Record Size: 130 bytes
========================
--- Reading test data ---
Total unread records: 5000
Test read 1: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000001+E
..........这中间数据读取与写入一致
Test read 5000: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409005000+E
Read test completed: 5000 records processed
=== Test Completed ===
=== 2 SPI Flash Manual Read/Write 5000 ===
Write progress: 100/5000
........
Write progress: 5000/5000
Write test completed. Unread records: 5000
=== Ring Buffer Status ===
Current Address: 0x0013D620
Total Records: 10000
Read Records: 5000
Unread Records: 5000
Max Capacity: 16131 records
Buffer Size: 2097152 bytes
Record Size: 130 bytes
========================
--- Reading test data ---
Total unread records: 5000
Test read 1: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000001+E
..........这中间数据读取与写入一致
Test read 5000: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409005000+E
Read test completed: 5000 records processed
=== Test Completed ===
=== 3 SPI Flash Manual Read/Write 5000 ===
Write progress: 100/5000
..........
Write progress: 5000/5000
Write test completed. Unread records: 5000
=== Ring Buffer Status ===
Current Address: 0x001DC130
Total Records: 15000
Read Records: 10000
Unread Records: 5000
Max Capacity: 16131 records
Buffer Size: 2097152 bytes
Record Size: 130 bytes
========================
--- Reading test data ---
Total unread records: 5000
Test read 1: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000001+E
..........这中间数据读取与写入一致
Test read 5000: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409005000+E
Read test completed: 5000 records processed
=== Test Completed ===
=== 4 SPI Flash Manual Read/Write 5000 ===
Write progress: 100/5000
........
Write progress: 5000/5000
Write test completed. Unread records: 5000
=== Ring Buffer Status ===
Current Address: 0x0007ACBA
Total Records: 20000
Read Records: 15000
Unread Records: 5000
Max Capacity: 16131 records
Buffer Size: 2097152 bytes
Record Size: 130 bytes
WARNING: Ring buffer has wrapped around!
========================
--- Reading test data ---
Total unread records: 5000
Test read 1: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000001+E
..........这中间数据读取与写入一致
Test read 1162: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409001162+E
从1162以后开始数据就开始错误了：
Test read 1163: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000022+E
Test read 1164: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000020+E
Test read 1165: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000024+E
Test read 1166: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000024+E
Test read 1167: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000026+E
Test read 1168: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000020+E
Test read 1169: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000028+E
Test read 1170: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000030+E
从1163-5000条之间的读取数据都是错误的
Test read 4994: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000800+E
Test read 4995: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000804+E
Test read 4996: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000804+E
Test read 4997: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000806+E
Test read 4998: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000800+E
Test read 4999: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409000808+E
Test read 5000: HY109S+119.96216+30.27593+063652.070725+9.5+1+6+0.0+3.75+31.5+3.0+3.0+-3.7+-1.3+0.0+0.00+22+8985231227852409001000+E
Read test completed: 5000 records processed
=== Test Completed ===
=== 5 SPI Flash Manual Read/Write 5000 ===
Write progress: 100/5000
Write progress: 200/5000
Write progress: 300/5000
Write progress: 400/5000
Write progress: 500/5000
Write progress: 600/5000
Write progress: 700/5000
Write progress: 800/5000
Write progress: 900/5000
Write progress: 1000/5000
Write progress: 1100/5000
Write progress: 1200/5000
Write progress: 1300/5000
Write progress: 1400/5000
Write progress: 1500/5000
Write progress: 1600/5000
Write progress: 1700/5000
Write progress: 1800/5000
Write progress: 1900/5000
Write progress: 2000/5000
Write progress: 2100/5000
Write progress: 2200/5000
Write progress: 2300/5000
Write progress: 2400/5000
Write progress: 2500/5000
Write progress: 2600/5000
Write progress: 2700/5000
Write progress: 2800/5000
Write progress: 2900/5000
Write progress: 3000/5000
Write progress: 3100/5000